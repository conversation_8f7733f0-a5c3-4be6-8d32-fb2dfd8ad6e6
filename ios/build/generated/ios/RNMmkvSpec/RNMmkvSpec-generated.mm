/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#import "RNMmkvSpec.h"


@implementation NativeMmkvPlatformContextSpecBase


- (void)setEventEmitterCallback:(EventEmitterCallbackWrapper *)eventEmitterCallbackWrapper
{
  _eventEmitterCallback = std::move(eventEmitterCallbackWrapper->_eventEmitterCallback);
}
@end


namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeMmkvPlatformContextSpecJSI_getBaseDirectory(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, StringKind, "getBaseDirectory", @selector(getBaseDirectory), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeMmkvPlatformContextSpecJSI_getAppGroupDirectory(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, StringKind, "getAppGroupDirectory", @selector(getAppGroupDirectory), args, count);
    }

  NativeMmkvPlatformContextSpecJSI::NativeMmkvPlatformContextSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["getBaseDirectory"] = MethodMetadata {0, __hostFunction_NativeMmkvPlatformContextSpecJSI_getBaseDirectory};
        
        
        methodMap_["getAppGroupDirectory"] = MethodMetadata {0, __hostFunction_NativeMmkvPlatformContextSpecJSI_getAppGroupDirectory};
        
  }
} // namespace facebook::react
