/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleH.js
 */

#pragma once

#include <ReactCommon/TurboModule.h>
#include <react/bridging/Bridging.h>

namespace facebook::react {


  class JSI_EXPORT NativeAppOpenModuleCxxSpecJSI : public TurboModule {
protected:
  NativeAppOpenModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void appOpenLoad(jsi::Runtime &rt, double requestId, jsi::String adUnitId, jsi::Object requestOptions) = 0;
  virtual jsi::Value appOpenShow(jsi::Runtime &rt, double requestId, jsi::String adUnitId, std::optional<jsi::Object> showOptions) = 0;

};

template <typename T>
class JSI_EXPORT NativeAppOpenModuleCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "RNGoogleMobileAdsAppOpenModule";

protected:
  NativeAppOpenModuleCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeAppOpenModuleCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeAppOpenModuleCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeAppOpenModuleCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void appOpenLoad(jsi::Runtime &rt, double requestId, jsi::String adUnitId, jsi::Object requestOptions) override {
      static_assert(
          bridging::getParameterCount(&T::appOpenLoad) == 4,
          "Expected appOpenLoad(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::appOpenLoad, jsInvoker_, instance_, std::move(requestId), std::move(adUnitId), std::move(requestOptions));
    }
    jsi::Value appOpenShow(jsi::Runtime &rt, double requestId, jsi::String adUnitId, std::optional<jsi::Object> showOptions) override {
      static_assert(
          bridging::getParameterCount(&T::appOpenShow) == 4,
          "Expected appOpenShow(...) to have 4 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::appOpenShow, jsInvoker_, instance_, std::move(requestId), std::move(adUnitId), std::move(showOptions));
    }

  private:
    friend class NativeAppOpenModuleCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


#pragma mark - NativeConsentModuleAdsConsentDebugGeography

enum class NativeConsentModuleAdsConsentDebugGeography { DISABLED, EEA, NOT_EEA, REGULATED_US_STATE, OTHER };

template <>
struct Bridging<NativeConsentModuleAdsConsentDebugGeography> {
  static NativeConsentModuleAdsConsentDebugGeography fromJs(jsi::Runtime &rt, const jsi::Value &rawValue) {
    double value = (double)rawValue.asNumber();
    if (value == 0) {
      return NativeConsentModuleAdsConsentDebugGeography::DISABLED;
    } else if (value == 1) {
      return NativeConsentModuleAdsConsentDebugGeography::EEA;
    } else if (value == 2) {
      return NativeConsentModuleAdsConsentDebugGeography::NOT_EEA;
    } else if (value == 3) {
      return NativeConsentModuleAdsConsentDebugGeography::REGULATED_US_STATE;
    } else if (value == 4) {
      return NativeConsentModuleAdsConsentDebugGeography::OTHER;
    } else {
      throw jsi::JSError(rt, "No appropriate enum member found for value");
    }
  }

  static jsi::Value toJs(jsi::Runtime &rt, NativeConsentModuleAdsConsentDebugGeography value) {
    if (value == NativeConsentModuleAdsConsentDebugGeography::DISABLED) {
      return bridging::toJs(rt, 0);
    } else if (value == NativeConsentModuleAdsConsentDebugGeography::EEA) {
      return bridging::toJs(rt, 1);
    } else if (value == NativeConsentModuleAdsConsentDebugGeography::NOT_EEA) {
      return bridging::toJs(rt, 2);
    } else if (value == NativeConsentModuleAdsConsentDebugGeography::REGULATED_US_STATE) {
      return bridging::toJs(rt, 3);
    } else if (value == NativeConsentModuleAdsConsentDebugGeography::OTHER) {
      return bridging::toJs(rt, 4);
    } else {
      throw jsi::JSError(rt, "No appropriate enum member found for enum value");
    }
  }
};

#pragma mark - NativeConsentModuleAdsConsentPrivacyOptionsRequirementStatus

enum class NativeConsentModuleAdsConsentPrivacyOptionsRequirementStatus { UNKNOWN, REQUIRED, NOT_REQUIRED };

template <>
struct Bridging<NativeConsentModuleAdsConsentPrivacyOptionsRequirementStatus> {
  static NativeConsentModuleAdsConsentPrivacyOptionsRequirementStatus fromJs(jsi::Runtime &rt, const jsi::String &rawValue) {
    std::string value = rawValue.utf8(rt);
    if (value == "UNKNOWN") {
      return NativeConsentModuleAdsConsentPrivacyOptionsRequirementStatus::UNKNOWN;
    } else if (value == "REQUIRED") {
      return NativeConsentModuleAdsConsentPrivacyOptionsRequirementStatus::REQUIRED;
    } else if (value == "NOT_REQUIRED") {
      return NativeConsentModuleAdsConsentPrivacyOptionsRequirementStatus::NOT_REQUIRED;
    } else {
      throw jsi::JSError(rt, "No appropriate enum member found for value");
    }
  }

  static jsi::String toJs(jsi::Runtime &rt, NativeConsentModuleAdsConsentPrivacyOptionsRequirementStatus value) {
    if (value == NativeConsentModuleAdsConsentPrivacyOptionsRequirementStatus::UNKNOWN) {
      return bridging::toJs(rt, "UNKNOWN");
    } else if (value == NativeConsentModuleAdsConsentPrivacyOptionsRequirementStatus::REQUIRED) {
      return bridging::toJs(rt, "REQUIRED");
    } else if (value == NativeConsentModuleAdsConsentPrivacyOptionsRequirementStatus::NOT_REQUIRED) {
      return bridging::toJs(rt, "NOT_REQUIRED");
    } else {
      throw jsi::JSError(rt, "No appropriate enum member found for enum value");
    }
  }
};

#pragma mark - NativeConsentModuleAdsConsentStatus

enum class NativeConsentModuleAdsConsentStatus { UNKNOWN, REQUIRED, NOT_REQUIRED, OBTAINED };

template <>
struct Bridging<NativeConsentModuleAdsConsentStatus> {
  static NativeConsentModuleAdsConsentStatus fromJs(jsi::Runtime &rt, const jsi::String &rawValue) {
    std::string value = rawValue.utf8(rt);
    if (value == "UNKNOWN") {
      return NativeConsentModuleAdsConsentStatus::UNKNOWN;
    } else if (value == "REQUIRED") {
      return NativeConsentModuleAdsConsentStatus::REQUIRED;
    } else if (value == "NOT_REQUIRED") {
      return NativeConsentModuleAdsConsentStatus::NOT_REQUIRED;
    } else if (value == "OBTAINED") {
      return NativeConsentModuleAdsConsentStatus::OBTAINED;
    } else {
      throw jsi::JSError(rt, "No appropriate enum member found for value");
    }
  }

  static jsi::String toJs(jsi::Runtime &rt, NativeConsentModuleAdsConsentStatus value) {
    if (value == NativeConsentModuleAdsConsentStatus::UNKNOWN) {
      return bridging::toJs(rt, "UNKNOWN");
    } else if (value == NativeConsentModuleAdsConsentStatus::REQUIRED) {
      return bridging::toJs(rt, "REQUIRED");
    } else if (value == NativeConsentModuleAdsConsentStatus::NOT_REQUIRED) {
      return bridging::toJs(rt, "NOT_REQUIRED");
    } else if (value == NativeConsentModuleAdsConsentStatus::OBTAINED) {
      return bridging::toJs(rt, "OBTAINED");
    } else {
      throw jsi::JSError(rt, "No appropriate enum member found for enum value");
    }
  }
};
  
#pragma mark - NativeConsentModuleAdsConsentInfo

template <typename P0, typename P1, typename P2, typename P3>
struct NativeConsentModuleAdsConsentInfo {
  P0 status;
  P1 canRequestAds;
  P2 privacyOptionsRequirementStatus;
  P3 isConsentFormAvailable;
  bool operator==(const NativeConsentModuleAdsConsentInfo &other) const {
    return status == other.status && canRequestAds == other.canRequestAds && privacyOptionsRequirementStatus == other.privacyOptionsRequirementStatus && isConsentFormAvailable == other.isConsentFormAvailable;
  }
};

template <typename T>
struct NativeConsentModuleAdsConsentInfoBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.status)>(rt, value.getProperty(rt, "status"), jsInvoker),
      bridging::fromJs<decltype(types.canRequestAds)>(rt, value.getProperty(rt, "canRequestAds"), jsInvoker),
      bridging::fromJs<decltype(types.privacyOptionsRequirementStatus)>(rt, value.getProperty(rt, "privacyOptionsRequirementStatus"), jsInvoker),
      bridging::fromJs<decltype(types.isConsentFormAvailable)>(rt, value.getProperty(rt, "isConsentFormAvailable"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::String statusToJs(jsi::Runtime &rt, decltype(types.status) value) {
    return bridging::toJs(rt, value);
  }

  static bool canRequestAdsToJs(jsi::Runtime &rt, decltype(types.canRequestAds) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String privacyOptionsRequirementStatusToJs(jsi::Runtime &rt, decltype(types.privacyOptionsRequirementStatus) value) {
    return bridging::toJs(rt, value);
  }

  static bool isConsentFormAvailableToJs(jsi::Runtime &rt, decltype(types.isConsentFormAvailable) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "status", bridging::toJs(rt, value.status, jsInvoker));
    result.setProperty(rt, "canRequestAds", bridging::toJs(rt, value.canRequestAds, jsInvoker));
    result.setProperty(rt, "privacyOptionsRequirementStatus", bridging::toJs(rt, value.privacyOptionsRequirementStatus, jsInvoker));
    result.setProperty(rt, "isConsentFormAvailable", bridging::toJs(rt, value.isConsentFormAvailable, jsInvoker));
    return result;
  }
};



#pragma mark - NativeConsentModuleAdsConsentInfoOptions

template <typename P0, typename P1, typename P2>
struct NativeConsentModuleAdsConsentInfoOptions {
  P0 debugGeography;
  P1 tagForUnderAgeOfConsent;
  P2 testDeviceIdentifiers;
  bool operator==(const NativeConsentModuleAdsConsentInfoOptions &other) const {
    return debugGeography == other.debugGeography && tagForUnderAgeOfConsent == other.tagForUnderAgeOfConsent && testDeviceIdentifiers == other.testDeviceIdentifiers;
  }
};

template <typename T>
struct NativeConsentModuleAdsConsentInfoOptionsBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.debugGeography)>(rt, value.getProperty(rt, "debugGeography"), jsInvoker),
      bridging::fromJs<decltype(types.tagForUnderAgeOfConsent)>(rt, value.getProperty(rt, "tagForUnderAgeOfConsent"), jsInvoker),
      bridging::fromJs<decltype(types.testDeviceIdentifiers)>(rt, value.getProperty(rt, "testDeviceIdentifiers"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::Value debugGeographyToJs(jsi::Runtime &rt, decltype(types.debugGeography) value) {
    return bridging::toJs(rt, value);
  }

  static bool tagForUnderAgeOfConsentToJs(jsi::Runtime &rt, decltype(types.tagForUnderAgeOfConsent) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Array testDeviceIdentifiersToJs(jsi::Runtime &rt, decltype(types.testDeviceIdentifiers) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    if (value.debugGeography) {
      result.setProperty(rt, "debugGeography", bridging::toJs(rt, value.debugGeography.value(), jsInvoker));
    }
    if (value.tagForUnderAgeOfConsent) {
      result.setProperty(rt, "tagForUnderAgeOfConsent", bridging::toJs(rt, value.tagForUnderAgeOfConsent.value(), jsInvoker));
    }
    if (value.testDeviceIdentifiers) {
      result.setProperty(rt, "testDeviceIdentifiers", bridging::toJs(rt, value.testDeviceIdentifiers.value(), jsInvoker));
    }
    return result;
  }
};

class JSI_EXPORT NativeConsentModuleCxxSpecJSI : public TurboModule {
protected:
  NativeConsentModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Value requestInfoUpdate(jsi::Runtime &rt, std::optional<jsi::Object> options) = 0;
  virtual jsi::Value showForm(jsi::Runtime &rt) = 0;
  virtual jsi::Value showPrivacyOptionsForm(jsi::Runtime &rt) = 0;
  virtual jsi::Value loadAndShowConsentFormIfRequired(jsi::Runtime &rt) = 0;
  virtual jsi::Value getConsentInfo(jsi::Runtime &rt) = 0;
  virtual jsi::Value getTCString(jsi::Runtime &rt) = 0;
  virtual jsi::Value getGdprApplies(jsi::Runtime &rt) = 0;
  virtual jsi::Value getPurposeConsents(jsi::Runtime &rt) = 0;
  virtual jsi::Value getPurposeLegitimateInterests(jsi::Runtime &rt) = 0;
  virtual void reset(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeConsentModuleCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "RNGoogleMobileAdsConsentModule";

protected:
  NativeConsentModuleCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeConsentModuleCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeConsentModuleCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeConsentModuleCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Value requestInfoUpdate(jsi::Runtime &rt, std::optional<jsi::Object> options) override {
      static_assert(
          bridging::getParameterCount(&T::requestInfoUpdate) == 2,
          "Expected requestInfoUpdate(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::requestInfoUpdate, jsInvoker_, instance_, std::move(options));
    }
    jsi::Value showForm(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::showForm) == 1,
          "Expected showForm(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::showForm, jsInvoker_, instance_);
    }
    jsi::Value showPrivacyOptionsForm(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::showPrivacyOptionsForm) == 1,
          "Expected showPrivacyOptionsForm(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::showPrivacyOptionsForm, jsInvoker_, instance_);
    }
    jsi::Value loadAndShowConsentFormIfRequired(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::loadAndShowConsentFormIfRequired) == 1,
          "Expected loadAndShowConsentFormIfRequired(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::loadAndShowConsentFormIfRequired, jsInvoker_, instance_);
    }
    jsi::Value getConsentInfo(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConsentInfo) == 1,
          "Expected getConsentInfo(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getConsentInfo, jsInvoker_, instance_);
    }
    jsi::Value getTCString(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getTCString) == 1,
          "Expected getTCString(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getTCString, jsInvoker_, instance_);
    }
    jsi::Value getGdprApplies(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getGdprApplies) == 1,
          "Expected getGdprApplies(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getGdprApplies, jsInvoker_, instance_);
    }
    jsi::Value getPurposeConsents(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getPurposeConsents) == 1,
          "Expected getPurposeConsents(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getPurposeConsents, jsInvoker_, instance_);
    }
    jsi::Value getPurposeLegitimateInterests(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getPurposeLegitimateInterests) == 1,
          "Expected getPurposeLegitimateInterests(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getPurposeLegitimateInterests, jsInvoker_, instance_);
    }
    void reset(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::reset) == 1,
          "Expected reset(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::reset, jsInvoker_, instance_);
    }

  private:
    friend class NativeConsentModuleCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeGoogleMobileAdsModuleCxxSpecJSI : public TurboModule {
protected:
  NativeGoogleMobileAdsModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual jsi::Value initialize(jsi::Runtime &rt) = 0;
  virtual jsi::Value setRequestConfiguration(jsi::Runtime &rt, std::optional<jsi::Object> requestConfiguration) = 0;
  virtual jsi::Value openAdInspector(jsi::Runtime &rt) = 0;
  virtual void openDebugMenu(jsi::Runtime &rt, jsi::String adUnit) = 0;
  virtual void setAppVolume(jsi::Runtime &rt, double volume) = 0;
  virtual void setAppMuted(jsi::Runtime &rt, bool muted) = 0;

};

template <typename T>
class JSI_EXPORT NativeGoogleMobileAdsModuleCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "RNGoogleMobileAdsModule";

protected:
  NativeGoogleMobileAdsModuleCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeGoogleMobileAdsModuleCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeGoogleMobileAdsModuleCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeGoogleMobileAdsModuleCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    jsi::Value initialize(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::initialize) == 1,
          "Expected initialize(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::initialize, jsInvoker_, instance_);
    }
    jsi::Value setRequestConfiguration(jsi::Runtime &rt, std::optional<jsi::Object> requestConfiguration) override {
      static_assert(
          bridging::getParameterCount(&T::setRequestConfiguration) == 2,
          "Expected setRequestConfiguration(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::setRequestConfiguration, jsInvoker_, instance_, std::move(requestConfiguration));
    }
    jsi::Value openAdInspector(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::openAdInspector) == 1,
          "Expected openAdInspector(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::openAdInspector, jsInvoker_, instance_);
    }
    void openDebugMenu(jsi::Runtime &rt, jsi::String adUnit) override {
      static_assert(
          bridging::getParameterCount(&T::openDebugMenu) == 2,
          "Expected openDebugMenu(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::openDebugMenu, jsInvoker_, instance_, std::move(adUnit));
    }
    void setAppVolume(jsi::Runtime &rt, double volume) override {
      static_assert(
          bridging::getParameterCount(&T::setAppVolume) == 2,
          "Expected setAppVolume(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setAppVolume, jsInvoker_, instance_, std::move(volume));
    }
    void setAppMuted(jsi::Runtime &rt, bool muted) override {
      static_assert(
          bridging::getParameterCount(&T::setAppMuted) == 2,
          "Expected setAppMuted(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setAppMuted, jsInvoker_, instance_, std::move(muted));
    }

  private:
    friend class NativeGoogleMobileAdsModuleCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativeGoogleMobileAdsNativeModuleNativeAdEventPayload

template <typename P0, typename P1>
struct NativeGoogleMobileAdsNativeModuleNativeAdEventPayload {
  P0 responseId;
  P1 type;
  bool operator==(const NativeGoogleMobileAdsNativeModuleNativeAdEventPayload &other) const {
    return responseId == other.responseId && type == other.type;
  }
};

template <typename T>
struct NativeGoogleMobileAdsNativeModuleNativeAdEventPayloadBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.responseId)>(rt, value.getProperty(rt, "responseId"), jsInvoker),
      bridging::fromJs<decltype(types.type)>(rt, value.getProperty(rt, "type"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::String responseIdToJs(jsi::Runtime &rt, decltype(types.responseId) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String typeToJs(jsi::Runtime &rt, decltype(types.type) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "responseId", bridging::toJs(rt, value.responseId, jsInvoker));
    result.setProperty(rt, "type", bridging::toJs(rt, value.type, jsInvoker));
    return result;
  }
};



#pragma mark - NativeGoogleMobileAdsNativeModuleNativeAdImage

template <typename P0, typename P1>
struct NativeGoogleMobileAdsNativeModuleNativeAdImage {
  P0 url;
  P1 scale;
  bool operator==(const NativeGoogleMobileAdsNativeModuleNativeAdImage &other) const {
    return url == other.url && scale == other.scale;
  }
};

template <typename T>
struct NativeGoogleMobileAdsNativeModuleNativeAdImageBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.url)>(rt, value.getProperty(rt, "url"), jsInvoker),
      bridging::fromJs<decltype(types.scale)>(rt, value.getProperty(rt, "scale"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::String urlToJs(jsi::Runtime &rt, decltype(types.url) value) {
    return bridging::toJs(rt, value);
  }

  static double scaleToJs(jsi::Runtime &rt, decltype(types.scale) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "url", bridging::toJs(rt, value.url, jsInvoker));
    result.setProperty(rt, "scale", bridging::toJs(rt, value.scale, jsInvoker));
    return result;
  }
};



#pragma mark - NativeGoogleMobileAdsNativeModuleNativeAdProps

template <typename P0, typename P1, typename P2, typename P3, typename P4, typename P5, typename P6, typename P7, typename P8, typename P9, typename P10, typename P11>
struct NativeGoogleMobileAdsNativeModuleNativeAdProps {
  P0 responseId;
  P1 advertiser;
  P2 body;
  P3 callToAction;
  P4 headline;
  P5 price;
  P6 store;
  P7 starRating;
  P8 icon;
  P9 images;
  P10 mediaContent;
  P11 extras;
  bool operator==(const NativeGoogleMobileAdsNativeModuleNativeAdProps &other) const {
    return responseId == other.responseId && advertiser == other.advertiser && body == other.body && callToAction == other.callToAction && headline == other.headline && price == other.price && store == other.store && starRating == other.starRating && icon == other.icon && images == other.images && mediaContent == other.mediaContent && extras == other.extras;
  }
};

template <typename T>
struct NativeGoogleMobileAdsNativeModuleNativeAdPropsBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.responseId)>(rt, value.getProperty(rt, "responseId"), jsInvoker),
      bridging::fromJs<decltype(types.advertiser)>(rt, value.getProperty(rt, "advertiser"), jsInvoker),
      bridging::fromJs<decltype(types.body)>(rt, value.getProperty(rt, "body"), jsInvoker),
      bridging::fromJs<decltype(types.callToAction)>(rt, value.getProperty(rt, "callToAction"), jsInvoker),
      bridging::fromJs<decltype(types.headline)>(rt, value.getProperty(rt, "headline"), jsInvoker),
      bridging::fromJs<decltype(types.price)>(rt, value.getProperty(rt, "price"), jsInvoker),
      bridging::fromJs<decltype(types.store)>(rt, value.getProperty(rt, "store"), jsInvoker),
      bridging::fromJs<decltype(types.starRating)>(rt, value.getProperty(rt, "starRating"), jsInvoker),
      bridging::fromJs<decltype(types.icon)>(rt, value.getProperty(rt, "icon"), jsInvoker),
      bridging::fromJs<decltype(types.images)>(rt, value.getProperty(rt, "images"), jsInvoker),
      bridging::fromJs<decltype(types.mediaContent)>(rt, value.getProperty(rt, "mediaContent"), jsInvoker),
      bridging::fromJs<decltype(types.extras)>(rt, value.getProperty(rt, "extras"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::String responseIdToJs(jsi::Runtime &rt, decltype(types.responseId) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::String> advertiserToJs(jsi::Runtime &rt, decltype(types.advertiser) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String bodyToJs(jsi::Runtime &rt, decltype(types.body) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String callToActionToJs(jsi::Runtime &rt, decltype(types.callToAction) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String headlineToJs(jsi::Runtime &rt, decltype(types.headline) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::String> priceToJs(jsi::Runtime &rt, decltype(types.price) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::String> storeToJs(jsi::Runtime &rt, decltype(types.store) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<double> starRatingToJs(jsi::Runtime &rt, decltype(types.starRating) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::Object> iconToJs(jsi::Runtime &rt, decltype(types.icon) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::Array> imagesToJs(jsi::Runtime &rt, decltype(types.images) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Object mediaContentToJs(jsi::Runtime &rt, decltype(types.mediaContent) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::Object> extrasToJs(jsi::Runtime &rt, decltype(types.extras) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "responseId", bridging::toJs(rt, value.responseId, jsInvoker));
    result.setProperty(rt, "advertiser", bridging::toJs(rt, value.advertiser, jsInvoker));
    result.setProperty(rt, "body", bridging::toJs(rt, value.body, jsInvoker));
    result.setProperty(rt, "callToAction", bridging::toJs(rt, value.callToAction, jsInvoker));
    result.setProperty(rt, "headline", bridging::toJs(rt, value.headline, jsInvoker));
    result.setProperty(rt, "price", bridging::toJs(rt, value.price, jsInvoker));
    result.setProperty(rt, "store", bridging::toJs(rt, value.store, jsInvoker));
    result.setProperty(rt, "starRating", bridging::toJs(rt, value.starRating, jsInvoker));
    result.setProperty(rt, "icon", bridging::toJs(rt, value.icon, jsInvoker));
    result.setProperty(rt, "images", bridging::toJs(rt, value.images, jsInvoker));
    result.setProperty(rt, "mediaContent", bridging::toJs(rt, value.mediaContent, jsInvoker));
    result.setProperty(rt, "extras", bridging::toJs(rt, value.extras, jsInvoker));
    return result;
  }
};



#pragma mark - NativeGoogleMobileAdsNativeModuleNativeMediaContent

template <typename P0, typename P1, typename P2>
struct NativeGoogleMobileAdsNativeModuleNativeMediaContent {
  P0 aspectRatio;
  P1 hasVideoContent;
  P2 duration;
  bool operator==(const NativeGoogleMobileAdsNativeModuleNativeMediaContent &other) const {
    return aspectRatio == other.aspectRatio && hasVideoContent == other.hasVideoContent && duration == other.duration;
  }
};

template <typename T>
struct NativeGoogleMobileAdsNativeModuleNativeMediaContentBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.aspectRatio)>(rt, value.getProperty(rt, "aspectRatio"), jsInvoker),
      bridging::fromJs<decltype(types.hasVideoContent)>(rt, value.getProperty(rt, "hasVideoContent"), jsInvoker),
      bridging::fromJs<decltype(types.duration)>(rt, value.getProperty(rt, "duration"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static double aspectRatioToJs(jsi::Runtime &rt, decltype(types.aspectRatio) value) {
    return bridging::toJs(rt, value);
  }

  static bool hasVideoContentToJs(jsi::Runtime &rt, decltype(types.hasVideoContent) value) {
    return bridging::toJs(rt, value);
  }

  static double durationToJs(jsi::Runtime &rt, decltype(types.duration) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "aspectRatio", bridging::toJs(rt, value.aspectRatio, jsInvoker));
    result.setProperty(rt, "hasVideoContent", bridging::toJs(rt, value.hasVideoContent, jsInvoker));
    result.setProperty(rt, "duration", bridging::toJs(rt, value.duration, jsInvoker));
    return result;
  }
};

class JSI_EXPORT NativeGoogleMobileAdsNativeModuleCxxSpecJSI : public TurboModule {
protected:
  NativeGoogleMobileAdsNativeModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Value load(jsi::Runtime &rt, jsi::String adUnitId, jsi::Object requestOptions) = 0;
  virtual void destroy(jsi::Runtime &rt, jsi::String responseId) = 0;

};

template <typename T>
class JSI_EXPORT NativeGoogleMobileAdsNativeModuleCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "RNGoogleMobileAdsNativeModule";

protected:
  NativeGoogleMobileAdsNativeModuleCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeGoogleMobileAdsNativeModuleCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}

  template <typename OnAdEventType> void emitOnAdEvent(OnAdEventType value) {
    static_assert(bridging::supportsFromJs<OnAdEventType, jsi::Object>, "value cannnot be converted to jsi::Object");
    static_cast<AsyncEventEmitter<jsi::Value>&>(*delegate_.eventEmitterMap_["onAdEvent"]).emit([jsInvoker = jsInvoker_, eventValue = value](jsi::Runtime& rt) -> jsi::Value {
      return bridging::toJs(rt, eventValue, jsInvoker);
    });
  }

private:
  class Delegate : public NativeGoogleMobileAdsNativeModuleCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeGoogleMobileAdsNativeModuleCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {
      eventEmitterMap_["onAdEvent"] = std::make_shared<AsyncEventEmitter<jsi::Value>>();
    }

    jsi::Value load(jsi::Runtime &rt, jsi::String adUnitId, jsi::Object requestOptions) override {
      static_assert(
          bridging::getParameterCount(&T::load) == 3,
          "Expected load(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::load, jsInvoker_, instance_, std::move(adUnitId), std::move(requestOptions));
    }
    void destroy(jsi::Runtime &rt, jsi::String responseId) override {
      static_assert(
          bridging::getParameterCount(&T::destroy) == 2,
          "Expected destroy(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::destroy, jsInvoker_, instance_, std::move(responseId));
    }

  private:
    friend class NativeGoogleMobileAdsNativeModuleCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeInterstitialModuleCxxSpecJSI : public TurboModule {
protected:
  NativeInterstitialModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void interstitialLoad(jsi::Runtime &rt, double requestId, jsi::String adUnitId, jsi::Object requestOptions) = 0;
  virtual jsi::Value interstitialShow(jsi::Runtime &rt, double requestId, jsi::String adUnitId, std::optional<jsi::Object> showOptions) = 0;

};

template <typename T>
class JSI_EXPORT NativeInterstitialModuleCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "RNGoogleMobileAdsInterstitialModule";

protected:
  NativeInterstitialModuleCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeInterstitialModuleCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeInterstitialModuleCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeInterstitialModuleCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void interstitialLoad(jsi::Runtime &rt, double requestId, jsi::String adUnitId, jsi::Object requestOptions) override {
      static_assert(
          bridging::getParameterCount(&T::interstitialLoad) == 4,
          "Expected interstitialLoad(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::interstitialLoad, jsInvoker_, instance_, std::move(requestId), std::move(adUnitId), std::move(requestOptions));
    }
    jsi::Value interstitialShow(jsi::Runtime &rt, double requestId, jsi::String adUnitId, std::optional<jsi::Object> showOptions) override {
      static_assert(
          bridging::getParameterCount(&T::interstitialShow) == 4,
          "Expected interstitialShow(...) to have 4 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::interstitialShow, jsInvoker_, instance_, std::move(requestId), std::move(adUnitId), std::move(showOptions));
    }

  private:
    friend class NativeInterstitialModuleCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeRewardedInterstitialModuleCxxSpecJSI : public TurboModule {
protected:
  NativeRewardedInterstitialModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void rewardedInterstitialLoad(jsi::Runtime &rt, double requestId, jsi::String adUnitId, jsi::Object requestOptions) = 0;
  virtual jsi::Value rewardedInterstitialShow(jsi::Runtime &rt, double requestId, jsi::String adUnitId, std::optional<jsi::Object> showOptions) = 0;

};

template <typename T>
class JSI_EXPORT NativeRewardedInterstitialModuleCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "RNGoogleMobileAdsRewardedInterstitialModule";

protected:
  NativeRewardedInterstitialModuleCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeRewardedInterstitialModuleCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeRewardedInterstitialModuleCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeRewardedInterstitialModuleCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void rewardedInterstitialLoad(jsi::Runtime &rt, double requestId, jsi::String adUnitId, jsi::Object requestOptions) override {
      static_assert(
          bridging::getParameterCount(&T::rewardedInterstitialLoad) == 4,
          "Expected rewardedInterstitialLoad(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::rewardedInterstitialLoad, jsInvoker_, instance_, std::move(requestId), std::move(adUnitId), std::move(requestOptions));
    }
    jsi::Value rewardedInterstitialShow(jsi::Runtime &rt, double requestId, jsi::String adUnitId, std::optional<jsi::Object> showOptions) override {
      static_assert(
          bridging::getParameterCount(&T::rewardedInterstitialShow) == 4,
          "Expected rewardedInterstitialShow(...) to have 4 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::rewardedInterstitialShow, jsInvoker_, instance_, std::move(requestId), std::move(adUnitId), std::move(showOptions));
    }

  private:
    friend class NativeRewardedInterstitialModuleCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeRewardedModuleCxxSpecJSI : public TurboModule {
protected:
  NativeRewardedModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void rewardedLoad(jsi::Runtime &rt, double requestId, jsi::String adUnitId, jsi::Object requestOptions) = 0;
  virtual jsi::Value rewardedShow(jsi::Runtime &rt, double requestId, jsi::String adUnitId, std::optional<jsi::Object> showOptions) = 0;

};

template <typename T>
class JSI_EXPORT NativeRewardedModuleCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "RNGoogleMobileAdsRewardedModule";

protected:
  NativeRewardedModuleCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeRewardedModuleCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeRewardedModuleCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeRewardedModuleCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void rewardedLoad(jsi::Runtime &rt, double requestId, jsi::String adUnitId, jsi::Object requestOptions) override {
      static_assert(
          bridging::getParameterCount(&T::rewardedLoad) == 4,
          "Expected rewardedLoad(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::rewardedLoad, jsInvoker_, instance_, std::move(requestId), std::move(adUnitId), std::move(requestOptions));
    }
    jsi::Value rewardedShow(jsi::Runtime &rt, double requestId, jsi::String adUnitId, std::optional<jsi::Object> showOptions) override {
      static_assert(
          bridging::getParameterCount(&T::rewardedShow) == 4,
          "Expected rewardedShow(...) to have 4 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::rewardedShow, jsInvoker_, instance_, std::move(requestId), std::move(adUnitId), std::move(showOptions));
    }

  private:
    friend class NativeRewardedModuleCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};

} // namespace facebook::react
