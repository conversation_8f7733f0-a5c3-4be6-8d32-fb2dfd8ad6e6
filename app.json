{"expo": {"name": "BCS-Question-Bank", "slug": "BCS-Question-Bank", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/bcs.png", "scheme": "bcsquestionbank", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.fazlerabbistat.BCSQuestionBank"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/bcs.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.fazlerabbistat.BCSQuestionBank"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/bcs.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["react-native-google-mobile-ads", {"androidAppId": "ca-app-pub-****************~**********", "iosAppId": "ca-app-pub-xxxxxxxx~xxxxxxxx"}]], "experiments": {"typedRoutes": true}}}